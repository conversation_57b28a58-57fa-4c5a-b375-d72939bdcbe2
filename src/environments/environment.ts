// <PERSON><PERSON><PERSON> fejlesztői környezet
import { Environment } from '@trendency/kesma-core';

export const environment: Environment = {
  production: false,
  type: 'local',
  apiUrl: '/publicapi/hu', // for proxy: '/publicapi/hu' then: npm run start-with-proxy
  secureApiUrl: 'https://kozponti-api.dev.trendency.hu/secureapi/hu',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '1559281357682288',
  googleClientId: '100010769979-7hvhinkuk40tv7vgrftrui2dll8dqcuf.apps.googleusercontent.com',
  siteUrl: 'http://localhost:4200',
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1',
  googleTagManager: 'GTM-N5752H',
  gemiusId: '0tWVfW_HLXWlMYWnlnOz6JciP4NIaEcyKHX8XeNMph7.l7',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '', // Disable sentry for localhost (empty dsn)
    tracingOrigins: ['http://localhost:4200', 'localhost'],
    sampleRate: 0.1,
    tracesSampleRate: 0.1,
    profilesSampleRate: 0.1,
  },
};
