@use 'shared' as *;

:host {
  display: block;

  mindmegette-article-card {
    margin-bottom: 0;
  }
  .kesma-swipe {
    ::ng-deep {
      .bottom-navigation {
        margin-top: 24px;
      }
    }
  }

  .title {
    font-size: 30px;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: -0.3px;
    text-align: center;
    margin-bottom: 32px;
  }

  ::ng-deep {
    mindmegette-article-card.article {
      .title {
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.16px;
        margin-bottom: 12px !important;
      }
    }
  }
}
