import { Injectable, Inject } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class GastroNavigationTrackerService {
  private readonly cameFromGastroSubject = new BehaviorSubject<boolean>(false);

  public readonly cameFromGastro$: Observable<boolean> = this.cameFromGastroSubject.asObservable();

  private previousUrl = '';
  private readonly gastroPattern = /^\/elmenyek/;
  private readonly targetPages = ['/bejelentkezes', '/hirlevel-feliratkozas'];
  private readonly sessionStorageKey = 'gastro_navigation_state';

  constructor(
    private readonly router: Router,
    @Inject(DOCUMENT) private readonly document: Document
  ) {
    this.setupNavigationTracking();
    this.checkInitialState();
  }

  private checkInitialState(): void {
    if (typeof window === 'undefined' || !window.sessionStorage) {
      return;
    }

    const currentUrl = this.router.url;

    if (this.isTargetPage(currentUrl)) {
      // Check session storage first
      const storedState = window.sessionStorage.getItem(this.sessionStorageKey);
      if (storedState === 'true') {
        this.cameFromGastroSubject.next(true);
        return;
      }

      // Check referrer as fallback
      const referrer = this.document.referrer;
      if (referrer && this.isGastroPage(this.extractPathFromUrl(referrer))) {
        this.cameFromGastroSubject.next(true);
        window.sessionStorage.setItem(this.sessionStorageKey, 'true');
        return;
      }
    }
  }

  private setupNavigationTracking(): void {
    this.router.events.pipe(filter((event): event is NavigationEnd => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      const currentUrl = event.urlAfterRedirects;

      if (this.isGastroPage(currentUrl)) {
        // Set session storage when on gastro pages
        if (typeof window !== 'undefined' && window.sessionStorage) {
          window.sessionStorage.setItem(this.sessionStorageKey, 'true');
        }
      } else if (this.isTargetPage(currentUrl)) {
        // Check if coming from gastro (SPA navigation or session storage)
        const cameFromGastro = this.isGastroPage(this.previousUrl) ||
          (typeof window !== 'undefined' && window.sessionStorage?.getItem(this.sessionStorageKey) === 'true');
        this.cameFromGastroSubject.next(cameFromGastro);
      } else if (!this.isTargetPage(currentUrl)) {
        // Clear session storage and reset flag when navigating to other pages
        if (typeof window !== 'undefined' && window.sessionStorage) {
          window.sessionStorage.removeItem(this.sessionStorageKey);
        }
        this.cameFromGastroSubject.next(false);
      }

      this.previousUrl = currentUrl;
    });
  }

  private isGastroPage(url: string): boolean {
    return this.gastroPattern.test(url);
  }

  private isTargetPage(url: string): boolean {
    return this.targetPages.some((page) => url.startsWith(page));
  }

  private extractPathFromUrl(fullUrl: string): string {
    try {
      const url = new URL(fullUrl);
      return url.pathname;
    } catch {
      return '';
    }
  }

  public getCameFromGastro(): boolean {
    return this.cameFromGastroSubject.value;
  }
}
