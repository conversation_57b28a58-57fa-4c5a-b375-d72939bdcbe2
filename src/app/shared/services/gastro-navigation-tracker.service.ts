import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class GastroNavigationTrackerService {
  private readonly cameFromGastroSubject = new BehaviorSubject<boolean>(false);

  public readonly cameFromGastro$: Observable<boolean> = this.cameFromGastroSubject.asObservable();

  private previousUrl = '';
  private readonly gastroPattern = /^\/elmenyek/;
  private readonly targetPages = ['/bejelentkezes', '/hirlevel-feliratkozas'];

  constructor(private readonly router: Router) {
    this.setupNavigationTracking();
  }

  private setupNavigationTracking(): void {
    this.router.events.pipe(filter((event): event is NavigationEnd => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      const currentUrl = event.urlAfterRedirects;

      if (this.isTargetPage(currentUrl)) {
        const cameFromGastro = this.isGastroPage(this.previousUrl);
        this.cameFromGastroSubject.next(cameFromGastro);
      } else if (!this.isTargetPage(currentUrl)) {
        this.cameFromGastroSubject.next(false);
      }

      this.previousUrl = currentUrl;
    });
  }

  private isGastroPage(url: string): boolean {
    return this.gastroPattern.test(url);
  }

  private isTargetPage(url: string): boolean {
    return this.targetPages.some((page) => url.startsWith(page));
  }

  public getCameFromGastro(): boolean {
    return this.cameFromGastroSubject.value;
  }
}
