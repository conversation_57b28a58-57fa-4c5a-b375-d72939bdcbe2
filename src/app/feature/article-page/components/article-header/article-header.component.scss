@use 'shared' as *;

:host {
  display: block;
  padding: 8px 0;

  @include media-breakpoint-up(md) {
    padding: 30px 0;
  }

  .article {
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-items: center;

    &-header {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 984px;
      align-items: center;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    &-wrapper {
      display: flex;
      gap: 8px;

      &-category {
        padding: 2px 6px;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.48px;
        text-transform: uppercase;
        color: var(--kui-white);
        background: var(--kui-pink-500);
        font-family: var(--kui-font-secondary);
        border-radius: 4px;
      }

      &-live {
        display: flex;
        align-items: center;
        gap: 4px;
        background: none;
        border: 1px solid var(--kui-pink-500);

        span {
          color: var(--kui-pink-500);
          font-family: var(--kui-font-secondary);
          font-size: 12px;
          font-weight: 500;
          line-height: 16px;
        }

        .mindmegette-icon-live {
          width: 16px;
          height: 16px;
        }
      }
    }

    &-title {
      color: var(--kui-gray-950);
      text-align: center;
      font-size: 40px;
      font-weight: 600;
      line-height: 48px;
      letter-spacing: -0.4px;

      @include media-breakpoint-down(sm) {
        width: 100%;
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
      }
    }

    &-pre-title {
      color: var(--kui-gray-950);
      text-align: center;
      font-family: var(--kui-font-secondary);
      font-size: 18px;
      font-weight: 400;
      line-height: 28px;
    }

    &-author {
      margin-top: 8px;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 6px;
      align-items: center;
      color: var(--kui-gray-600);
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.12px;

      &-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 50%;
        margin-right: 8px;
      }

      &-name {
        color: var(--kui-gray-600);
      }

      .separator {
        width: 1px;
        height: 12px;
        background: var(--kui-gray-300);
      }
    }

    &-thumbnail {
      border-radius: 8px;
      width: 100%;
      max-height: 246px;
      object-fit: cover;

      @include media-breakpoint-up(md) {
        max-height: 692px;
      }

      &-wrapper {
        width: 100%;
        position: relative;

        //isGuaranteeType
        .mindmegette-icon-guarantee {
          width: 104px;
          height: 104px;
          position: absolute;
          border-top-left-radius: 8px;
          top: 0;
          left: 0;
        }
      }

      &-meta {
        margin-top: 8px;
        color: var(--kui-gray-400);
        text-align: center;
        font-family: var(--kui-font-secondary);
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.48px;
        text-transform: uppercase;
      }
    }
  }
}
