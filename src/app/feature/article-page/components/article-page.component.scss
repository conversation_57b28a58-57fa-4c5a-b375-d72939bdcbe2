@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);
  color: var(--kui-black);
  margin-bottom: 80px;

  @include media-breakpoint-down(md) {
    margin-bottom: 60px;
  }

  mindmegette-wysiwyg-box {
    ::ng-deep {
      .custom-text-style {
        &.underlined-text {
          margin: 0;
          @include media-breakpoint-down(sm) {
            padding: unset;
          }
        }
      }

      .block-content {
        > * {
          margin-bottom: 24px;
        }
      }

      :last-child {
        margin-bottom: 0;
      }
    }
  }

  .wrapper {
    &.with-aside {
      margin-top: 28px;

      @include media-breakpoint-down(md) {
        margin-top: 16px;
      }

      aside {
        margin-bottom: 0;

        app-sidebar {
          ::ng-deep {
            app-layout {
              margin-bottom: 0;

              .layout-element {
                padding-bottom: 0;
              }
            }
          }
        }
      }
    }

    .left-column {
      display: flex;
      flex-direction: column;
      gap: 24px;

      @include media-breakpoint-down(lg) {
        gap: 16px;
      }
    }

    .center > * {
      margin-bottom: 24px;
    }
  }

  .article {
    &-lead {
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.16px;
      margin-bottom: 24px;
    }

    &-embed-pr-advert {
      width: 100%;
      display: flex;
      justify-content: center;
      margin: 10px auto;
    }
  }

  ::ng-deep .external-recommendations {
    margin-top: 32px;
  }

  .recipe-card {
    display: grid;
    grid-template-columns: 3fr 2fr;
    grid-template-rows: 1fr;
    grid-column-gap: 0;
    grid-row-gap: 0;
    padding: 32px 0;
    border-radius: 8px;
    background-color: var(--kui-white);
    outline: 1px solid var(--kui-gray-100);
    @include media-breakpoint-down(lg) {
      display: flex;
      flex-direction: column-reverse;
      margin: 0;
      row-gap: 16px;
      padding: 16px;
    }

    &-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 32px;
      align-self: stretch;
      margin: 41px 0;
      @include media-breakpoint-down(lg) {
        gap: 8px;
        margin: 0;
      }

      &-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        align-self: stretch;

        .category-label-wrapper {
          display: flex;
          flex-direction: row;
          gap: 4px;
        }

        .recipe-title {
          font-family: var(--kui-font-secondary);
          color: var(--kui-black);
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 26px;
          text-align: center;
          margin: 0px 16px;
        }

        .recipe-data {
          display: flex;
          flex-direction: row;
          gap: 12px;
        }

        .recipe-button {
          display: flex;
          padding: 10px 20px;
          justify-content: center;
          align-items: center;
          gap: 6px;
          margin-top: 24px;
          border-radius: 8px;
          font-family: var(--kui-font-secondary);
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          letter-spacing: 0.16px;
          color: var(--kui-white);
          background-color: var(--kui-green-700);
          cursor: pointer;
          @include media-breakpoint-down(lg) {
            margin-top: 8px;
            width: 100%;
          }

          i {
            width: 20px;
            height: 20px;
          }
        }
      }
    }

    &-right {
      display: flex;
      padding-right: 32px;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;
      gap: 10px;
      align-self: stretch;

      img {
        border-radius: 8px;
        object-fit: cover;
      }

      @include media-breakpoint-down(lg) {
        padding: 0;
        width: 100%;
      }
    }

    &-details {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      overflow: hidden;
    }

    &-meta {
      padding: 0 12px;
      color: var(--kui-gray-950);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;

      &.divider {
        border-right: 1px solid var(--kui-gray-300);
      }
    }
  }

  .reload {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    background-color: var(--kui-pink-500);
    color: var(--kui-white);
    padding: 10px 20px;
    border-radius: 8px;
    font-family: var(--kui-font-secondary);
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    cursor: pointer;
    margin-bottom: 16px;

    @include media-breakpoint-up(sm) {
      font-size: 18px;
      margin-bottom: 20px;
    }

    .mindmegette-icon-reload {
      width: 16px;
      height: 16px;

      @include media-breakpoint-up(sm) {
        width: 20px;
        height: 20px;
      }
    }
  }

  .reload-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    @include media-breakpoint-up(sm) {
      margin-bottom: 20px;
    }

    &-info {
      font-family: var(--kui-font-secondary);
      font-size: 12px;
      font-weight: 700;
      line-height: 16px;

      @include media-breakpoint-up(sm) {
        font-size: 14px;
        line-height: 20px;
      }
    }

    &-number {
      color: var(--kui-gray-400);
      font-family: var(--kui-font-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .card {
    padding: 16px;
    background-color: var(--kui-white);
    border: 1px solid var(--kui-gray-100);
    border-radius: 12px;
    margin-bottom: 20px;

    @include media-breakpoint-up(sm) {
      padding: 32px;
      margin-bottom: 32px;
    }

    &-title {
      font-family: var(--kui-font-secondary);
      font-size: 18px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.18px;
      margin-bottom: 12px;

      @include media-breakpoint-up(md) {
        font-family: var(--kui-font-primary);
        font-size: 24px;
        font-weight: 600;
        line-height: 28px;
        letter-spacing: 0.12px;
        margin-bottom: 16px;
      }
    }
  }

  .date-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .date {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .hhmm {
    background-color: var(--kui-pink-500);
    border-radius: 4px;
    padding: 2px 8px;
    font-family: var(--kui-font-secondary);
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: var(--kui-white);
  }

  .full-date {
    font-family: var(--kui-font-secondary);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: var(--kui-gray-400);
  }

  mindmegette-wysiwyg-box::ng-deep .table-wrapper {
    overflow-x: auto;

    table {
      display: block;
    }
  }
}

.pp-scroll-target {
  position: relative;
  top: -80px;

  @include media-breakpoint-up(md) {
    top: -100px;
  }
}
