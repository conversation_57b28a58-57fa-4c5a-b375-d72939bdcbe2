@use 'shared' as *;

.author {
  &-list {
    > .wrapper {
      margin: 30px auto;
    }

    h1 {
      color: var(--kui-gray-950);
      font-size: 40px;
      font-weight: 600;
      line-height: 48px;
      letter-spacing: -0.4px;
      margin-bottom: 32px;
    }

    mindmegette-pager {
      display: block;
      margin: 32px 0;
    }
  }

  &-item {
    border-radius: 8px;
    border: 1px solid var(--kui-gray-100);
    background-color: var(--kui-white);
    padding: 20px 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
  }

  &-avatar {
    img {
      border-radius: 8px;
      min-width: 100px;
      width: 100px;
      height: 100px;
      object-fit: cover;

      @include media-breakpoint-down(md) {
        min-width: 80px;
        width: 80px;
        height: 80px;
      }
    }
  }

  &-name {
    color: var(--kui-gray-950);
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;
    cursor: pointer;
  }

  &-rank {
    font-family: var(--kui-font-secondary);
    font-size: 18px;
    line-height: 28px;
    color: var(--kui-gray-600);
    margin-top: 4px;
  }

  &-more-button {
    margin-left: auto;
    align-self: flex-end;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    margin-right: 4px;
    color: var(--kui-green-700);
    gap: 5px;
    transition: color 0.3s ease;

    &:hover {
      color: var(--kui-green-800);
    }

    .icon {
      min-width: 20px;
      width: 20px;
      height: 20px;
    }

    @include media-breakpoint-down(md) {
      align-self: center;

      span {
        display: none;
      }

      .icon {
        min-width: 40px;
        width: 40px;
        height: 40px;
      }
    }
  }
}
