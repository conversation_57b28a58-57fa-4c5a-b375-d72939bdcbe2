import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class GastroNavigationTrackerService {
  private readonly cameFromGastroSubject = new BehaviorSubject<boolean>(false);

  public readonly cameFromGastro$: Observable<boolean> = this.cameFromGastroSubject.asObservable();

  private previousUrl = '';
  private readonly gastroPattern = /^\/elmenyek/;
  private readonly targetPages = ['/bejelentkezes', '/hirlevel-feliratkozas'];

  constructor(private readonly router: Router) {
    this.setupNavigationTracking();
  }

  private setupNavigationTracking(): void {
    this.router.events.pipe(filter((event): event is NavigationEnd => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      const currentUrl = event.urlAfterRedirects;

      // Check if we're navigating to a target page (login or newsletter)
      if (this.isTargetPage(currentUrl)) {
        // Check if the previous URL was a gastro page
        const cameFromGastro = this.isGastroPage(this.previousUrl);
        this.cameFromGastroSubject.next(cameFromGastro);
      } else if (!this.isTargetPage(currentUrl)) {
        // Reset the flag when navigating to other pages (not target pages)
        this.cameFromGastroSubject.next(false);
      }

      this.previousUrl = currentUrl;
    });
  }

  private isGastroPage(url: string): boolean {
    return this.gastroPattern.test(url);
  }

  private isTargetPage(url: string): boolean {
    return this.targetPages.some((page) => url.startsWith(page));
  }

  public getCameFromGastro(): boolean {
    return this.cameFromGastroSubject.value;
  }
}
