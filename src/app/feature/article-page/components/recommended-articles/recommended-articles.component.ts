import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent, IconComponent, KesmaSwipeComponent, SwipeBreakpoints } from '@trendency/kesma-ui';
import { ArticleCardComponent, MindmegetteArticleCardType, RecipeCardType } from '../../../../shared';

@Component({
  selector: 'app-recommended-articles',
  templateUrl: './recommended-articles.component.html',
  styleUrls: ['./recommended-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleCardComponent, IconComponent, KesmaSwipeComponent],
})
export class RecommendedArticlesComponent extends BaseComponent<ArticleCard[]> {
  @Input() title: string = 'Ajánlott tartalmak';

  readonly ArticleCardType = MindmegetteArticleCardType;

  breakpoints: SwipeBreakpoints = {
    default: {
      itemCount: 1.5,
      gap: '16px',
    },
    550: {
      itemCount: 2,
      gap: '16px',
    },
    650: {
      itemCount: 3,
      gap: '16px',
    },
  };

  protected readonly RecipeCardType = RecipeCardType;
}
