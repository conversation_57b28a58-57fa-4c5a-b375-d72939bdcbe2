import { AsyncPipe, DOCUMENT, NgClass, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { StorageService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  ApiResult,
  BreakingBlock,
  InitResolverData,
  MediaworksFooterCompactComponent,
  PAGE_TYPES,
  PortfolioItem,
  PortfolioResponse,
  SecondaryFilterAdvertType,
  SimplifiedMenuItem,
  Sponsorship,
  TapeType,
  User,
} from '@trendency/kesma-ui';
import shuffle from 'lodash-es/shuffle';
import { combineLatest, interval, Observable, of, Subject, switchMap, take } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith, takeUntil } from 'rxjs/operators';

import { ApiService, AuthService, GastroService, GastroNavigationTrackerService, ProfileHeaderService, SecureApiService, SponsorshipService } from '../../services';
import { WelcomeLayerComponent } from '../welcome-layer/welcome-layer.component';
import { LoginPopupComponent } from '../login-popup/login-popup.component';
import { FooterComponent } from '../footer/footer.component';
import { GastroFooterComponent } from '../gastro-footer/gastro-footer.component';
import { HeaderComponent } from '../header/header.component';
import { GastroHeaderComponent } from '../gastro-header/gastro-header.component';
import { BreakingStripComponent } from '../breaking-strip/breaking-strip.component';
import { BackendHighlightedExperienceOccasion } from '../../../feature/gastro/definitions/experience.definitions';
import { ShoppingListService } from '../../services/shopping-list.service';
import { TapeComponent } from '../tape/tape.component';
import { ExperienceHighlightedOccasionsTapeComponent } from '../../../feature/gastro/components/experience-highlighted-occasions-tape/experience-highlighted-occasions-tape.component';
import { ShoppingCartItem } from '../../definitions';
import { NgxLoadingBar } from '@ngx-loading-bar/core';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    AdvertisementAdoceanComponent,
    BreakingStripComponent,
    GastroHeaderComponent,
    ExperienceHighlightedOccasionsTapeComponent,
    HeaderComponent,
    NgClass,
    RouterOutlet,
    GastroFooterComponent,
    FooterComponent,
    MediaworksFooterCompactComponent,
    LoginPopupComponent,
    WelcomeLayerComponent,
    AsyncPipe,
    TapeComponent,
    NgxLoadingBar,
  ],
})
export class BaseComponent implements OnInit, OnDestroy, AfterViewInit {
  mainMenu: SimplifiedMenuItem[] = [];
  rightMenu: SimplifiedMenuItem[] = [];
  footerTop: SimplifiedMenuItem[] = [];
  footerBottom: SimplifiedMenuItem[] = [];
  highlightedTape: TapeType[] = [];
  gastroMainMenu: SimplifiedMenuItem[] = [];
  gastroRightMenu: SimplifiedMenuItem[] = [];
  gastroFooterTop: SimplifiedMenuItem[] = [];
  highlightedOccasions: BackendHighlightedExperienceOccasion[] = [];

  breakingNews?: BreakingBlock;
  breakingStripIsClosed = true;
  isArticleUrl: boolean;
  isHomePage: boolean;

  adverts?: AdvertisementsByMedium;
  backgroundAdvert?: Record<'desktop' | 'mobile', Advertisement>;

  isAdblockerActive: boolean;
  areAdsInitiated = false;

  mediaworksFooter: PortfolioItem[];

  sponsorshipService = inject(SponsorshipService);

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );

  isTapeHidden$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isTapeHidden'] === true)
  );

  showProfileHeader = false;

  currentYear: number = new Date().getFullYear();
  isGastro$ = this.gastroService.isGastro$;
  cameFromGastro$ = inject(GastroNavigationTrackerService).cameFromGastro$;
  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly apiService: ApiService,
    private readonly authService: AuthService,
    private readonly secureApiService: SecureApiService,
    private readonly storageService: StorageService,
    private readonly profileHeaderService: ProfileHeaderService,
    private readonly shoppingListService: ShoppingListService,
    private readonly gastroService: GastroService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  get user(): User | undefined {
    return this.authService.currentUser;
  }

  ngOnInit(): void {
    this.gastroService.handleChanges();
    // Check user at first page load (async, not to block UI) to display header differently if user is logged in
    this.authService
      .isAuthenticated()
      .pipe(
        switchMap((isAuthenticated) => {
          return isAuthenticated ? this.secureApiService.getShoppingList() : of({} as ApiResult<ShoppingCartItem[]>);
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(({ data }) => {
        this.shoppingListService.list$.next(data);
        this.changeRef.markForCheck();
      });

    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this.document?.location) {
            return of({} as AdvertisementsByMedium);
          }

          const [_, path1, path2] = this.document?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl = !isNaN(parseInt(path2, 10));
          this.resetAds();
          if (this.isArticleUrl) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], AdvertisementsByMedium>((ads) => {
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

              return this.adStoreAdo.separateAdsByMedium(
                ads,
                headerMediumSeparator.page,
                headerMediumSeparator.ads,
                SecondaryFilterAdvertType.REPLACEABLE,
                PAGE_TYPES.other_pages
              );
            }),
            delayWhen((ads) =>
              this.isArticleUrl
                ? this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.desktop?.layer?.masterId ||
                  this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.mobile?.layer?.masterId
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((adverts) => {
        this.adverts = adverts;
        const backgroundAd = 'background' as AdvertisementBannerName;
        this.backgroundAdvert = {
          desktop: adverts?.desktop?.[backgroundAd],
          mobile: adverts?.mobile?.[backgroundAd],
        };
        this.areAdsInitiated = true;
        this.changeRef.detectChanges();
      });

    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter = data.data;
        this.changeRef.detectChanges();
      });

    const responseData: InitResolverData & {
      highlightedSponsorship: Sponsorship;
      highlightedOccasions: BackendHighlightedExperienceOccasion[];
    } = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;
    this.highlightedOccasions = responseData?.highlightedOccasions;

    this.breakingStripIsClosed = this.utils.isBrowser() ? this.storageService.getLocalStorageData('breakingNews') === this.breakingNews?.slug : true;

    const {
      menu: { header_0, header_1, header_gastro, footer_0, footer_1, footer_gastro },
    } = responseData || {};
    this.mainMenu = header_0 ?? [];
    this.rightMenu = header_1 ?? [];
    this.footerTop = footer_0 ?? [];
    this.footerBottom = footer_1 ?? [];
    this.gastroFooterTop = [...this.gastroService.gastroMenuStaticElements, ...(footer_gastro ?? [])];
    this.gastroMainMenu = this.gastroService.gastroMenuStaticElements;
    this.gastroRightMenu = header_gastro ?? [];

    // Maximum 20 elemet kell megjeleníteni, de lehetőleg mindig különbözőeket.
    // KESMA-18193
    const tapeItems = responseData?.init?.highlightInTapeItems ?? [];
    const shuffledTapeItems = shuffle(tapeItems);
    const { highlightedSponsorship } = responseData;
    if (!Array.isArray(highlightedSponsorship)) {
      this.sponsorshipService.setHighlightedSponsorShip(highlightedSponsorship);
    }

    this.highlightedTape = shuffledTapeItems.slice(0, 20);

    this.changeRef.detectChanges();

    // Enable sticky layout debugging.
    this.enableStickyLayoutDebug();
    this.profileHeaderService.showProfileHeader$.pipe(takeUntil(this.unsubscribe$)).subscribe((showProfile) => {
      this.showProfileHeader = showProfile;
      this.changeRef.detectChanges();
    });
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  closeBreakingStrip(): void {
    this.utils.isBrowser() && this.storageService.setLocalStorageData('breakingNews', this.breakingNews?.slug);
    this.breakingStripIsClosed = true;
  }

  /**
   * Sets a CSS background color for the sticky elements. This could be useful for debugging.
   */
  enableStickyLayoutDebug(): void {
    if (this.utils.isBrowser() && this.route.snapshot.queryParams['stickyBg'] === '1') {
      (this.document.querySelector(':root') as any)['style'].setProperty('--mme-sticky-bg', 'lightblue');
    }
  }

  private baseElementPageTypeSwitch(path: string): {
    page: string;
    ads: AdvertisementBannerName[];
  } {
    const ads = [...ALL_BANNER_LIST, 'background'] as AdvertisementBannerName[];

    switch (path) {
      case '':
        this.isHomePage = true;
        return {
          page: PAGE_TYPES.main_page,
          ads,
        };
      default:
        this.isHomePage = false;
        return {
          page: this.adStoreAdo.articleParentCategory$.getValue(),
          ads,
        };
    }
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.areAdsInitiated = false;
    this.changeRef.detectChanges();
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return (this.isAdblockerActive = typeof (<any>window).ado !== 'object');
  }
}
