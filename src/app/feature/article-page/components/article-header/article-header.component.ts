import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Article, BaseComponent, FocusPointDirective, MinuteToMinuteState, UpdatedAtPipe } from '@trendency/kesma-ui';
import { NgIf, registerLocaleData } from '@angular/common';
import localeHu from '@angular/common/locales/hu';
import { PublishDatePipe } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';

registerLocaleData(localeHu);

@Component({
  selector: 'app-article-header',
  templateUrl: 'article-header.component.html',
  styleUrls: ['article-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, FocusPointDirective, PublishDatePipe, UpdatedAtPipe],
})
export class ArticleHeaderComponent extends BaseComponent<Article> {
  readonly MinuteToMinuteState = MinuteToMinuteState;

  get publishDates(): {
    original: Date;
    updated?: Date;
  } {
    const firstPublishDate = this.data?.firstPublishDate;
    const latestPublishDate = this.data?.publishDate;

    if (!firstPublishDate || !latestPublishDate || firstPublishDate.getTime() === latestPublishDate.getTime()) {
      return {
        original: latestPublishDate ?? firstPublishDate ?? new Date(),
      };
    }

    return {
      original: firstPublishDate,
      updated: latestPublishDate,
    };
  }
}
