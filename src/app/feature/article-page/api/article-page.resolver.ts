import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, share } from 'rxjs';
import {
  ApiResult,
  Article,
  ArticleCard,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleSocial,
  BackendArticleSearchResult,
  mapBackendArticleDataToArticleCard,
  RecommendationsData,
} from '@trendency/kesma-ui';
import { catchError, map, switchMap } from 'rxjs/operators';
import { ApiService, ArticleService } from '../../../shared';
import { CacheService } from '@shared/services/cache.service';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { environment } from '../../../../environments/environment';
import type { Response } from 'express';

export type ArticlePageResolverData = ArticleResolverData & {
  mostReadArticles?: ArticleCard[];
  socialCount: ArticleSocial;
  freshArticles: ArticleCard[];
};

@Injectable()
export class ArticlePageResolver {
  constructor(
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly apiService: ApiService,
    private readonly cacheService: CacheService,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ArticlePageResolverData> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;
    const previewHash = params.previewHash;
    const categorySlug = params.categorySlug;
    const articleSlug = params.previewHash ? 'cikk-elonezet' : params.articleSlug;
    const previewType = params.previewType ? params.previewType : 'accepted';

    const mostReadArticles$ = this.apiService.getArticles(3, true).pipe(
      map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))),
      catchError(() => of({}))
    ) as Observable<ArticleCard[]>;

    const url = `${categorySlug}/${articleSlug}`;

    const article$: Observable<ApiResult<Article>> = (
      previewHash
        ? this.articleService.getArticlePreview(articleSlug, previewHash, previewType)
        : this.articleService.getArticle(categorySlug, articleSlug).pipe(
            switchMap((article) => {
              this.cacheService.setCache(article?.meta);
              if (!article?.data?.isOpinion) {
                return of(article);
              }
              const publicAuthor = article.data?.publicAuthor || 'Mindmegette';
              return this.apiService.getOpinionAuthor(publicAuthor).pipe(
                map((articles) => ({
                  ...article,
                  articles,
                }))
              );
            })
          )
    ).pipe(share());

    const freshArticles$: Observable<ArticleCard[]> = this.apiService.getArticles(16).pipe(
      map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))),
      map((articles) => articles.filter((article) => article.slug !== articleSlug).slice(0, 15))
    );

    const socialCount$: Observable<ArticleSocial> = article$.pipe(
      switchMap((data) => {
        return this.apiService.getSocialDataByType(data.data.id, 'article');
      })
    );

    let request$: Observable<[ApiResult<Article>, ApiResult<RecommendationsData>, ArticleCard[], ArticleSocial, ArticleCard[]]>;

    if (previewHash) {
      request$ = forkJoin([article$, of({} as ApiResult<RecommendationsData>), of({} as ArticleCard[]), socialCount$, freshArticles$]);
    } else {
      request$ = forkJoin([
        article$,
        this.articleService.getArticleRecommendations(articleSlug, categorySlug).pipe(catchError(() => of({} as ApiResult<RecommendationsData>))),
        mostReadArticles$,
        socialCount$,
        freshArticles$,
      ]);
    }

    return request$
      .pipe(
        catchError(() => {
          return this.redirectOldArticleUrls();
        })
      )
      .pipe(
        map(
          ([article, recommendations, mostReadArticles, socialCount, freshArticles]): ArticlePageResolverData => ({
            article,
            mostReadArticles,
            recommendations,
            articleSlug,
            categorySlug,
            url,
            socialCount,
            freshArticles,
          })
        )
      );
  }

  private redirectOldArticleUrls(): Observable<any> {
    const currentUrl = this.seoService.currentUrl;
    return this.articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      map(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          window.location.href = url;
        } else if (url && this.response) {
          this.response.status(301);
          /*
           * port is missing from the response and `process.env.PORT` reflects
           * the devserver's port when running w/ local devserver -> replacing w/ the predefined
           */
          if (url.match(/^https?:\/\/localhost\//)) {
            url = url.replace(/^https?:\/\/localhost/, environment.siteUrl as string);
          }
          this.response.setHeader('location', url);
        } else {
          this.router.navigate(['/', '404'], {
            skipLocationChange: true,
          });
        }
        return of({});
      })
    );
  }
}
