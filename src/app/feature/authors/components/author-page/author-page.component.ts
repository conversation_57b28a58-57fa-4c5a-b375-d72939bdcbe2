import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SchemaOrgService, SeoService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  ArticlesByDate,
  backendDateToDate,
  createCanonicalUrlForPageablePage,
  getStructuredDataForProfilePage,
  LayoutElementContentType,
  LimitableMeta,
  PAGE_TYPES,
  PortalConfigSetting,
} from '@trendency/kesma-ui';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import {
  ArticleCardComponent,
  authorPageMetaInfo,
  BackendAuthorData,
  getStructuredDataForRecipeList,
  MindmegetteArticleCardType,
  MindmegettePagerComponent,
  MindmegetteSimpleButtonComponent,
  PortalConfigService,
  RecipeCardComponent,
  RecipeCardType,
} from '../../../../shared';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';
import { NgFor, NgIf, SlicePipe } from '@angular/common';
import { BackendExperienceOccasionListItem } from '../../../gastro/definitions/experience.definitions';
import { GastroAuthorExperienceBoxComponent } from '../../../gastro/components/gastro-author-experience-box/gastro-author-experience-box.component';

@Component({
  selector: 'app-author-page',
  templateUrl: './author-page.component.html',
  styleUrls: ['./author-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    GastroAuthorExperienceBoxComponent,
    FormsModule,
    NgSelectModule,
    NgFor,
    AdvertisementAdoceanComponent,
    SlicePipe,
    MindmegetteSimpleButtonComponent,
    RecipeCardComponent,
    ArticleCardComponent,
    MindmegettePagerComponent,
  ],
})
export class AuthorPageComponent implements OnInit, OnDestroy {
  excludeArticleId$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);

  articlesByDate: ArticlesByDate[] = [];
  author: BackendAuthorData;
  limitable?: LimitableMeta;

  adverts?: AdvertisementsByMedium;

  filters: Record<string, string | undefined> = {
    global_filter: undefined,
    'publishDate_order[]': 'desc',
  };

  publishDateSorts = [
    {
      label: 'Legfrissebb',
      value: 'desc',
    },
    {
      label: 'Legrégebbi',
      value: 'asc',
    },
  ];

  readonly PortalConfigSetting = PortalConfigSetting;
  readonly MindmegetteArticleCardType = MindmegetteArticleCardType;
  readonly MindmegetteRecipeCardType = RecipeCardType;
  readonly LayoutElementContentType = LayoutElementContentType;

  private readonly destroy$: Subject<boolean> = new Subject();
  authorExperiences: BackendExperienceOccasionListItem[];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly schemaService: SchemaOrgService,
    public readonly portalConfigService: PortalConfigService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(({ data: { articles, limitable, author, authorExperiences } }) => {
      this.authorExperiences = authorExperiences;
      this.articlesByDate = this.groupDataByDate(articles);
      this.limitable = limitable;
      this.author = author;

      const recipes = articles?.filter((article: ArticleCard) => article?.contentType === LayoutElementContentType.RECIPE);
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(getStructuredDataForProfilePage(this.author as any, environment?.siteUrl ?? ''));

      if (recipes?.length) {
        this.schemaService.insertSchema(getStructuredDataForRecipeList(recipes as any, environment?.siteUrl ?? ''));
      }

      const ids = articles.map((article: ArticleCard) => article.id).filter((id: string) => !!id) as string[];
      this.excludeArticleId$.next(ids || []);

      this.initAds();
      this.setMetaData();
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  onSearch(): void {
    const filters = this.filters;

    // Remove empty props
    Object.entries(filters).forEach(([key, value]) => {
      if (!value) {
        delete filters[key];
      }
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { ...filters },
    });
  }

  private initAds(): void {
    this.resetAds();

    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, PAGE_TYPES.other_pages);
      this.cdr.detectChanges();
    });
  }

  private setMetaData(): void {
    const page = (this.limitable?.pageCurrent || 0) + 1;
    this.seo.setMetaData(authorPageMetaInfo(this.author.public_author_name, page), { skipSeoMetaCheck: true });
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { skipSeoMetaCheck: true });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private groupDataByDate(articles: ArticleCard[]): ArticlesByDate[] {
    return articles.reduce<ArticlesByDate[]>((acc, item) => {
      const date = format(backendDateToDate(item.publishDate as string) ?? new Date(), 'yyyy. MMMM', { locale: hu });
      let group = acc.find((g) => {
        return g.date === date;
      });
      if (!group) {
        group = { date, articles: [] };
        acc.push(group);
      }
      group?.articles.push(item);
      return acc;
    }, []);
  }
}
