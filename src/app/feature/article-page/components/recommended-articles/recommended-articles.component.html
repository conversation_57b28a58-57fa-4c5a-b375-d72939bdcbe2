<h3 class="title">{{ title }}</h3>
<ng-template #itemTemplate let-article="data">
  <mindmegette-article-card class="article" [data]="article" [styleID]="ArticleCardType.TopImageLeftAlignedCard" [hasBackground]="true">
  </mindmegette-article-card>
</ng-template>
<ng-template #previousNavigation><kesma-icon name="mindmegette-icon-green-left-arrow" /></ng-template>
<ng-template #nextNavigation><kesma-icon name="mindmegette-icon-green-right-arrow" /></ng-template>
<div
  kesma-swipe
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="(data?.length || 0) > 3"
  [usePagination]="(data?.length || 0) > 3"
  [data]="data"
  [breakpoints]="breakpoints"
></div>
