import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, input, OnDestroy, OnInit } from '@angular/core';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, AdvertisementsByMedium, PAGE_TYPES } from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AsyncPipe, NgIf } from '@angular/common';
import { GastroNavigationTrackerService } from '../../services';

@Component({
  selector: 'app-sticky-image-wrapper',
  templateUrl: './sticky-image-wrapper.component.html',
  styleUrls: ['./sticky-image-wrapper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe, AdvertisementAdoceanComponent],
})
export class StickyImageWrapperComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<void> = new Subject<void>();

  readonly prizeDrawEndDate = new Date('2025.04.13. 23:59').getTime();
  isNewsletterSignup = input<boolean>(false);
  isActivePeriod = computed(() => new Date().getTime() <= this.prizeDrawEndDate);
  readonly basicNewsletterBackgroundImage = '/assets/images/sticky-bg-image.jpg';
  readonly prizeDrawNewsletterBackgroundImage = '/assets/images/mme_feliratkozo_720x732.jpg';

  adverts?: AdvertisementsByMedium;
  cameFromGastro$ = inject(GastroNavigationTrackerService).cameFromGastro$;

  constructor(
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
  protected initAds(): void {
    this.resetAds();

    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, PAGE_TYPES.other_pages);
      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }
}
