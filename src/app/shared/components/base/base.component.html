<ngx-loading-bar [includeSpinner]="false" height="2px" color="#fff"></ngx-loading-bar>

@if ((isGastro$ | async) === false && (cameFromGastro$ | async) === false) {
  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
}

<app-breaking-strip (closeClicked)="closeBreakingStrip()" *ngIf="!breakingStripIsClosed && breakingNews?.title" [data]="breakingNews"></app-breaking-strip>

@if (isGastro$ | async) {
  <app-gastro-header [mainMenu]="gastroMainMenu" [rightMenu]="gastroRightMenu" [user]="user"></app-gastro-header>
  <app-experience-highlighted-occasions-tape [data]="highlightedOccasions" *ngIf="highlightedOccasions?.length"></app-experience-highlighted-occasions-tape>
} @else {
  <app-header
    [hasBreakingNews]="!breakingStripIsClosed && !!breakingNews?.title"
    [isHomePage]="isHomePage"
    [mainMenu]="mainMenu"
    [rightMenu]="rightMenu"
    [showStickyHeader]="showProfileHeader"
    [user]="user"
  ></app-header>
}

<mindmegette-tape
  *ngIf="(isGastro$ | async) !== true && (isTapeHidden$ | async) !== true && this.highlightedTape?.length"
  [data]="highlightedTape"
></mindmegette-tape>

@if ((isGastro$ | async) === false && (cameFromGastro$ | async) === false) {
  <kesma-advertisement-adocean
    *ngIf="backgroundAdvert?.desktop as ad"
    [ad]="ad"
    [hasNoParentHeight]="true"
    [style]="{
      margin: 'var(--ad-margin)',
    }"
  >
  </kesma-advertisement-adocean>
  <kesma-advertisement-adocean
    *ngIf="backgroundAdvert?.mobile as ad"
    [ad]="ad"
    [hasNoParentHeight]="true"
    [style]="{
      margin: 'var(--ad-margin)',
    }"
  >
  </kesma-advertisement-adocean>

  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.leaderboard_1 as ad"
    [ad]="ad"
    [hasNoParentHeight]="true"
    [style]="{
      margin: 'var(--ad-margin)',
    }"
  >
  </kesma-advertisement-adocean>
}

<div [ngClass]="{ 'content-wrap-full-width': (isFullWidth$ | async) === true }" class="content-wrap">
  <router-outlet></router-outlet>
</div>

@if ((isGastro$ | async || (cameFromGastro$ | async) === false)) {
  <app-gastro-footer (openCookieSettings)="openCookieSettings()" [footerMenuTop]="gastroFooterTop" [footerMenuBottom]="footerBottom"></app-gastro-footer>
} @else {
  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.leaderboard_footer as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
    }"
  >
  </kesma-advertisement-adocean>
  <kesma-advertisement-adocean
    *ngIf="adverts?.mobile?.mobilrectangle_footer as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
    }"
  >
  </kesma-advertisement-adocean>

  <app-footer (openCookieSettings)="openCookieSettings()" [footerMenuBottom]="footerBottom" [footerMenuTop]="footerTop"></app-footer>
}
<kesma-mediaworks-footer-compact [data]="mediaworksFooter"></kesma-mediaworks-footer-compact>
<div class="footer-bottom">
  <span>© {{ currentYear }} A Mindmegette kiadója a Mediaworks Hungary Zrt.</span>
</div>

<app-login-popup></app-login-popup>

<app-welcome-layer></app-welcome-layer>
