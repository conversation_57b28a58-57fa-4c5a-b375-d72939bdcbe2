import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, of, share, switchMap, take, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { mapSocialAuthorToAuthor } from '../utils/author-mapper';
import { SeoService } from '@trendency/kesma-core';
import {
  ArticleSearchResult,
  BackendRecipeSearchResponse,
  LimitableMeta,
  PortalConfigSetting,
  RedirectService,
  RedirectService as KesmaRedirectService,
} from '@trendency/kesma-ui';
import { ApiService, AuthorData, PortalConfigService, searchResultToArticleCard } from '../../../shared';
import { GastroApiService } from '../../gastro/shared/gastro-api.service';
import { BackendExperienceOccasionList } from '../../gastro/definitions/experience.definitions';
import { backendRecipeSearchResponseToRecipeCard } from '../../search-page/search-page.utils';

@Injectable()
export class AuthorPageResolver {
  constructor(
    private readonly router: Router,
    private readonly apiService: ApiService,
    private readonly portalConfigService: PortalConfigService,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}
  private readonly kesmaRedirectService = inject(KesmaRedirectService);
  private readonly gastroService = inject(GastroApiService);

  resolve(route: ActivatedRouteSnapshot): Observable<{
    articles: any[]; // ArticleCard or RecipeCard
    limitable: LimitableMeta;
    author?: AuthorData;
    authorExperiences: BackendExperienceOccasionList[];
  }> {
    const authorSlug = route.paramMap.get('authorSlug');

    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`);
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const publicAuthor$ = this.apiService.getPublicAuthorSocial(authorSlug ?? '').pipe(
      share(),
      take(1),
      map(({ data }) => mapSocialAuthorToAuthor(data))
    );

    const authorExperience$ = this.gastroService.getExperienceOccasionList({ authorSlug: authorSlug }).pipe(
      take(1),
      map(({ data }) => data),
      catchError(() => {
        return of([]);
      })
    );

    const articlesObservable$ = publicAuthor$.pipe(
      take(1),
      switchMap((author) => {
        if (!author?.id) {
          this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
          throwError(() => 'Nincs ilyen szerző');
        }
        const authorFilter = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M)
          ? { 'author[]': `${author.id}` }
          : { author: author.publicAuthorName };
        return this.apiService
          .searchArticles({
            page_limit: `${currentPage}`,
            rowCount_limit: '20',
            global_filter: route.queryParams['global_filter'] || '',
            'publishDate_order[]': route.queryParams['publishDate_order[]'] === 'asc' ? 'asc' : 'desc',
            ...authorFilter,
          })
          .pipe(
            tap((res) => {
              if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res.data)) {
                this.kesmaRedirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
              }
            })
          );
      })
    );

    return forkJoin({
      articles: articlesObservable$,
      author: publicAuthor$,
      authorExperiences: authorExperience$,
    }).pipe(
      map(({ articles, author, authorExperiences }) => ({
        articles: articles?.data?.map((response: BackendRecipeSearchResponse) =>
          response.contentType !== 'recipe'
            ? searchResultToArticleCard(response as unknown as ArticleSearchResult)
            : {
                ...backendRecipeSearchResponseToRecipeCard(response),
                publishDate: response?.publishDate,
                contentType: response?.contentType,
              }
        ),
        limitable: articles?.meta?.limitable,
        author,
        authorExperiences,
      })),
      catchError((err) => {
        this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
