import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  PAGE_TYPES,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { BackendAuthorData, createMMETitle, defaultMetaInfo, MindmegettePagerComponent } from '../../../../shared';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-author-list',
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, NgIf, AdvertisementAdoceanComponent, AsyncPipe, MindmegettePagerComponent],
})
export class AuthorListComponent implements OnInit, OnDestroy {
  adverts: AdvertisementsByMedium;
  adPageType = PAGE_TYPES.other_pages;

  authors$: Observable<BackendAuthorData[]> = this.route.data.pipe(map((res) => res['data']?.['data']));
  limitables$: Observable<LimitableMeta> = this.route.data.pipe(map((res) => res['data']?.['meta'].limitable));

  private readonly unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  trackBySlug(_: number, item: BackendAuthorData): string {
    return item.slug ?? '';
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
    const title = createMMETitle('Szerzők');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    };

    this.seo.setMetaData(metaData);
  }

  private initAds(): void {
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads, this.adPageType);
      this.cdr.markForCheck();
    });
  }
}
