<kesma-layout
  [class.row-rounded-corners]="isGastroPage"
  [breakingNews]="breakingNews"
  [configuration]="configuration"
  [layoutType]="layoutType ?? LayoutPageTypes.HOME"
  [structure]="structure"
  [adPageType]="adPageType"
  [blockTitleRef]="blockTitles"
  [contentComponentsRef]="contentComponents"
  [contentComponentWrapperRef]="contentComponentsWrapper"
  [contentComponentInnerWrapperRef]="contentComponentsInnerWrapper"
  [blockTitleWrapperRef]="blockTitleWrapper"
  [editorFrameSize]="editorFrameSize"
></kesma-layout>

<ng-template #blockTitles let-layoutElement="layoutElement" let-desktopWidth="desktopWidth">
  @if (layoutElement.contentType !== LayoutElementContentType.OFFER_BOX) {
    <mindmegette-block-title
      [data]="layoutElement.blockTitle ?? { text: 'Blokk cím' }"
      [desktopWidth]="layoutType !== LayoutPageTypes.SIDEBAR ? desktopWidth : 3"
      [isHaveCustomBackground]="layoutElement.backgroundColor"
    >
    </mindmegette-block-title>
  }
</ng-template>

<ng-template #contentComponents let-layoutElement="layoutElement" let-index="index" let-desktopWidth="desktopWidth">
  @if (layoutElement.ad && layoutElement.contentType === LayoutElementContentType.Ad && (isGastro$ | async) === false && (cameFromGastro$ | async) === false) {
    @if (layoutType === LayoutPageTypes.SIDEBAR) {
      <mindmegette-advertisement-adocean-sidebar [ad]="layoutElement.ad"></mindmegette-advertisement-adocean-sidebar>
    } @else {
      <kesma-advertisement-adocean [ad]="layoutElement.ad"></kesma-advertisement-adocean>
    }
  } @else {
    <ng-container
      [layoutElement]="layoutElement"
      [index]="index"
      [desktopWidth]="desktopWidth"
      [layoutPageType]="layoutType"
      [isExperienceOccasionList]="isExperienceOccasionList"
      [isGastroPage]="isGastroPage"
      appLayoutDynamicRenderer
    >
    </ng-container>
  }
</ng-template>
