@use 'shared' as *;

:host {
  display: block;
  padding: 28px 0;

  h1 {
    color: var(--kui-gray-950);
    font-family: var(--kui-font-primary);
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 48px;
    letter-spacing: -0.4px;

    @include media-breakpoint-down(md) {
      text-align: center;
      font-size: 30px;
      font-style: normal;
      font-weight: 600;
      line-height: 36px;
      letter-spacing: -0.3px;
    }
  }

  .author {
    &-avatar {
      aspect-ratio: 1;
      border-radius: 8px;
      object-fit: cover;
      width: 100%;
    }

    &-rank {
      font-size: 14px;
      font-family: var(--kui-font-secondary);
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 0.7px;
      text-transform: uppercase;
      color: var(--kui-gray-300);
    }

    &-description {
      font-family: var(--kui-font-secondary);
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.16px;
      color: var(--kui-gray-900);
      margin: 16px 0;
    }

    &-details {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      gap: 32px;

      @include media-breakpoint-down(md) {
        align-items: center;
        text-align: center;
        flex-direction: column-reverse;
        gap: 10px;
      }
    }

    &-title {
      color: var(--kui-gray-900);
      font-family: var(--kui-font-secondary);
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.18px;
    }
  }

  .author-page-date-group {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin: 32px 0 0;
  }

  .sides {
    display: flex;
    margin-bottom: 50px;
    gap: 32px;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }

    .left-side {
      max-width: 440px;
      width: 100%;

      @include media-breakpoint-down(lg) {
        max-width: 100%;
      }
    }

    .right-side {
      width: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .social-icons {
    display: flex;
    gap: 8px;

    .icon {
      min-width: 36px;
      min-height: 36px;
    }
  }

  .results-label {
    font-family: var(--kui-font-primary);
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0.12px;
    color: var(--kui-gray-950);

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 26px;
    }

    sup {
      font-weight: 400;
      font-size: 14px;

      @include media-breakpoint-down(sm) {
        font-size: 12px;
      }
    }
  }

  .articles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 32px;
    margin: 32px 0;

    ::ng-deep {
      mindmegette-recipe-card {
        margin-bottom: unset;
      }
    }

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
      grid-gap: 24px;
    }
  }

  ::ng-deep .external-recommendations {
    margin-top: 80px;
    grid-gap: 32px !important;

    @include media-breakpoint-down(md) {
      margin-top: 32px;
      grid-gap: 16px !important;
    }
  }

  mindmegette-article-card,
  mindmegette-block-title {
    margin-bottom: 0;
  }

  .author-filter {
    display: flex;
    gap: 16px;
    margin: 0 0 32px;
    align-items: center;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-search {
      display: flex;
      width: 100%;
      gap: 16px;

      &-input {
        flex: 1;
        position: relative;

        .icon {
          width: 24px;
          height: 24px;
          position: absolute;
          top: calc(50% - 12px);
          left: 12px;

          @include media-breakpoint-down(sm) {
            width: 16px;
            height: 16px;
            position: absolute;
            top: calc(50% - 8px);
            left: 12px;
          }
        }

        .mindmegette-form-input {
          margin-bottom: 0;
          padding-left: 48px;

          @include media-breakpoint-down(sm) {
            padding-left: 40px;
          }
        }
      }
    }

    &-select {
      .mindmegette-form-select {
        margin-bottom: 0;
      }
    }
  }

  .no-result {
    margin-top: 20px;
  }
}
