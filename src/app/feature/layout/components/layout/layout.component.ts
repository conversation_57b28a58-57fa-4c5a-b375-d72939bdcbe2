import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, TemplateRef, ViewChild } from '@angular/core';
import { AdvertisementAdoceanSidebarComponent } from '@shared/components/advertisement-adocean-sidebar/advertisement-adocean-sidebar.component';
import {
  AdvertisementAdoceanComponent,
  BlockWrapperTemplateData,
  BreakingNews,
  LayoutComponent as KesmaLayoutComponent,
  LayoutContentItemWrapperTemplateData,
  LayoutContentParams,
  LayoutElementContentConfiguration,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutPageType,
  PAGE_TYPES,
  provideLayoutDataExtractors,
} from '@trendency/kesma-ui';
import { BlockTitleComponent, GastroService, GastroNavigationTrackerService } from '../../../../shared';
import { MINDMEGETTE_EXTRACTOR_CONFIG } from '../../extractors/extractor.config';
import { LayoutRendererDirective } from './layout.renderer.directive';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [KesmaLayoutComponent, BlockTitleComponent, LayoutRendererDirective, AsyncPipe, AdvertisementAdoceanSidebarComponent, AdvertisementAdoceanComponent],
  providers: [provideLayoutDataExtractors(MINDMEGETTE_EXTRACTOR_CONFIG, true)],
})
export class LayoutComponent {
  @Input() adPageType = PAGE_TYPES.other_pages;
  @Input() structure: LayoutElementRow[];
  @Input() configuration: LayoutElementContentConfiguration[];
  @Input() layoutType?: LayoutPageType;
  @Input() breakingNews: BreakingNews[] = [];
  @Input() contentComponentsWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() contentComponentsInnerWrapper: TemplateRef<LayoutContentItemWrapperTemplateData>;
  @Input() blockTitleWrapper: TemplateRef<BlockWrapperTemplateData>;
  @Input() editorFrameSize?: 'desktop' | 'mobile';
  @Input() isExperienceOccasionList = false;
  @Input() isGastroPage = false;

  @ViewChild('contentComponents', {
    read: TemplateRef,
    static: false,
  })
  contentComponents: TemplateRef<LayoutContentParams>;

  readonly LayoutPageTypes = LayoutPageType;
  readonly LayoutElementContentType = LayoutElementContentType;

  readonly isGastro$ = inject(GastroService).isGastro$;
  readonly cameFromGastro$ = inject(GastroNavigationTrackerService).cameFromGastro$;
}
